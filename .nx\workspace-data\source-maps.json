{"apps/admin-frontend": {"root": ["apps/admin-frontend/package.json", "nx/core/package-json"], "name": ["apps/admin-frontend/package.json", "nx/core/package-json"], "tags": ["apps/admin-frontend/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.js": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start.executor": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start.options": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start.metadata": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start.options.script": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start.metadata.scriptContent": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start.metadata.runCommand": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start:windows": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start:windows.executor": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start:windows.options": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start:windows.metadata": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start:windows.options.script": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start:windows.metadata.scriptContent": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start:windows.metadata.runCommand": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject.executor": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject.options": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject.metadata": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject.options.script": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject.metadata.scriptContent": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject.metadata.runCommand": ["apps/admin-frontend/package.json", "nx/core/package-json"]}, "apps/app-frontend": {"root": ["apps/app-frontend/package.json", "nx/core/package-json"], "name": ["apps/app-frontend/package.json", "nx/core/package-json"], "tags": ["apps/app-frontend/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.js": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start.executor": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start.options": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start.metadata": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start.options.script": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start.metadata.scriptContent": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start.metadata.runCommand": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject.executor": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject.options": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject.metadata": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject.options.script": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject.metadata.scriptContent": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject.metadata.runCommand": ["apps/app-frontend/package.json", "nx/core/package-json"]}, "packages/shared-types": {"root": ["packages/shared-types/package.json", "nx/core/package-json"], "name": ["packages/shared-types/package.json", "nx/core/package-json"], "tags": ["packages/shared-types/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/shared-types/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/shared-types/package.json", "nx/core/package-json"], "metadata.js": ["packages/shared-types/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/shared-types/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/shared-types/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/shared-types/package.json", "nx/core/package-json"], "targets": ["packages/shared-types/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["packages/shared-types/package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["packages/shared-types/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["packages/shared-types/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["packages/shared-types/package.json", "nx/core/package-json"]}, "packages/ui-components": {"root": ["packages/ui-components/package.json", "nx/core/package-json"], "name": ["packages/ui-components/package.json", "nx/core/package-json"], "tags": ["packages/ui-components/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/ui-components/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/ui-components/package.json", "nx/core/package-json"], "metadata.js": ["packages/ui-components/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/ui-components/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/ui-components/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/ui-components/package.json", "nx/core/package-json"], "targets": ["packages/ui-components/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["packages/ui-components/package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["packages/ui-components/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["packages/ui-components/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["packages/ui-components/package.json", "nx/core/package-json"]}}