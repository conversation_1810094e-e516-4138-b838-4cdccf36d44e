"""
Base schemas with common functionality for consistent API responses
"""
from pydantic import BaseModel, field_validator
from typing import Any
import uuid


class BaseResponseModel(BaseModel):
    """
    Base response model with automatic UUID to string conversion
    
    All response models should inherit from this to ensure consistent
    UUID handling across the API.
    """
    
    @field_validator('*', mode='before')
    @classmethod
    def convert_uuid_to_str(cls, v: Any) -> Any:
        """Convert UUID objects to strings for JSON serialization"""
        if isinstance(v, uuid.UUID):
            return str(v)
        return v

    class Config:
        from_attributes = True
        # Enable automatic conversion from SQLAlchemy models
        arbitrary_types_allowed = True


class TenantScopedResponseModel(BaseResponseModel):
    """
    Base model for tenant-scoped responses
    
    Includes tenant_id field and automatic UUID conversion
    """
    tenant_id: str
    
    
class BaseCreateModel(BaseModel):
    """Base model for create requests"""
    
    class Config:
        # Validate assignment to catch errors early
        validate_assignment = True
        # Use enum values instead of names
        use_enum_values = True


class BaseUpdateModel(BaseModel):
    """Base model for update requests"""
    
    class Config:
        # Validate assignment to catch errors early
        validate_assignment = True
        # Use enum values instead of names
        use_enum_values = True
        # Allow partial updates
        extra = "forbid"
