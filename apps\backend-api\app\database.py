from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from app.config import settings
import logging

logger = logging.getLogger(__name__)

# Create engine with proper configuration
engine = create_engine(
    settings.database_url,
    poolclass=StaticPool,
    connect_args={
        "check_same_thread": False,
        "options": "-c timezone=UTC"
    } if "sqlite" in settings.database_url else {"options": "-c timezone=UTC"},
    echo=settings.debug,
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()


def get_db():
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def set_tenant_context(db, tenant_id: str):
    """Set the tenant context for Row-Level Security"""
    try:
        logger.info(f"Setting tenant context to: {tenant_id}")
        db.execute(text("SELECT set_config('app.current_tenant_id', :tenant_id, true)"),
                  {"tenant_id": tenant_id})
        db.commit()
        logger.info(f"Successfully set tenant context to: {tenant_id}")
    except Exception as e:
        logger.error(f"Failed to set tenant context: {e}")
        db.rollback()
        raise


def clear_tenant_context(db):
    """Clear the tenant context"""
    try:
        db.execute(text("SELECT set_config('app.current_tenant_id', '', true)"))
        db.commit()
        logger.debug("Cleared tenant context")
    except Exception as e:
        logger.error(f"Failed to clear tenant context: {e}")
        db.rollback()
        raise
