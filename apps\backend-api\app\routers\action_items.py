from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from app.dependencies.auth import CurrentTenantUser, TenantScopedDB
from app.models.action_item import ActionItem
from app.models.user import TenantUser
from app.utils.permissions import Permission, check_permission
from app.routers.base import handle_database_error

router = APIRouter()


@router.get("/", response_model=List[dict])
async def list_action_items(
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB,
    skip: int = 0,
    limit: int = 100,
    completed: Optional[bool] = None,
    priority: Optional[str] = None,
    category: Optional[str] = None
):
    """List action items for current tenant"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACTION_ITEMS_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    query = db.query(ActionItem).filter(ActionItem.tenant_id == tenant_user.tenant_id)
    
    # Apply filters
    if completed is not None:
        query = query.filter(ActionItem.is_completed == completed)
    
    if priority:
        query = query.filter(ActionItem.priority == priority)
    
    if category:
        query = query.filter(ActionItem.category == category)
    
    # Order by priority and creation date
    priority_order = {"urgent": 1, "high": 2, "medium": 3, "low": 4}
    action_items = query.offset(skip).limit(limit).all()
    
    # Sort by priority and creation date
    action_items.sort(key=lambda x: (priority_order.get(x.priority, 5), x.created_at))
    
    return [
        {
            "id": item.id,
            "title": item.title,
            "description": item.description,
            "priority": item.priority,
            "category": item.category,
            "is_completed": item.is_completed,
            "created_at": item.created_at,
            "invoice_id": item.invoice_id,
            "assigned_user": {
                "id": item.user.id,
                "email": item.user.email
            } if item.user else None
        }
        for item in action_items
    ]


@router.get("/{action_item_id}")
async def get_action_item(
    action_item_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Get action item details"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACTION_ITEMS_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    action_item = db.query(ActionItem).filter(
        ActionItem.id == action_item_id,
        ActionItem.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not action_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Action item not found"
        )
    
    return {
        "id": action_item.id,
        "title": action_item.title,
        "description": action_item.description,
        "priority": action_item.priority,
        "category": action_item.category,
        "is_completed": action_item.is_completed,
        "resolution_notes": action_item.resolution_notes,
        "created_at": action_item.created_at,
        "updated_at": action_item.updated_at,
        "invoice_id": action_item.invoice_id,
        "assigned_user": {
            "id": action_item.user.id,
            "email": action_item.user.email
        } if action_item.user else None,
        "completed_by": action_item.completed_by,
        "invoice": {
            "id": action_item.invoice.id,
            "supplier_name": action_item.invoice.supplier_name,
            "status": action_item.invoice.status
        } if action_item.invoice else None
    }


@router.put("/{action_item_id}/complete")
async def complete_action_item(
    action_item_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB,
    resolution_notes: Optional[str] = None
):
    """Mark action item as completed"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACTION_ITEMS_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    action_item = db.query(ActionItem).filter(
        ActionItem.id == action_item_id,
        ActionItem.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not action_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Action item not found"
        )
    
    if action_item.is_completed:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Action item is already completed"
        )
    
    # Mark as completed
    action_item.is_completed = True
    action_item.completed_by = tenant_user.user_id
    action_item.resolution_notes = resolution_notes
    
    try:
        db.commit()
    except Exception as e:
        db.rollback()
        raise handle_database_error(e)
    
    return {"message": "Action item marked as completed"}


@router.put("/{action_item_id}/reopen")
async def reopen_action_item(
    action_item_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Reopen a completed action item"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACTION_ITEMS_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    action_item = db.query(ActionItem).filter(
        ActionItem.id == action_item_id,
        ActionItem.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not action_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Action item not found"
        )
    
    if not action_item.is_completed:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Action item is not completed"
        )
    
    # Reopen
    action_item.is_completed = False
    action_item.completed_by = None
    action_item.resolution_notes = None
    
    try:
        db.commit()
    except Exception as e:
        db.rollback()
        raise handle_database_error(e)
    
    return {"message": "Action item reopened"}


@router.delete("/{action_item_id}")
async def delete_action_item(
    action_item_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Delete an action item"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.ACTION_ITEMS_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    action_item = db.query(ActionItem).filter(
        ActionItem.id == action_item_id,
        ActionItem.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not action_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Action item not found"
        )
    
    try:
        db.delete(action_item)
        db.commit()
    except Exception as e:
        db.rollback()
        raise handle_database_error(e)
    
    return {"message": "Action item deleted successfully"}
