{"name": "admin-frontend", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/admin-frontend/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/admin-frontend", "index": "apps/admin-frontend/public/index.html", "baseHref": "/", "main": "apps/admin-frontend/src/index.tsx", "polyfills": "apps/admin-frontend/src/polyfills.ts", "tsConfig": "apps/admin-frontend/tsconfig.app.json", "assets": ["apps/admin-frontend/public/favicon.ico", "apps/admin-frontend/public"], "styles": ["apps/admin-frontend/src/index.css"], "scripts": [], "webpackConfig": "@nx/react/plugins/webpack"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true}, "production": {"fileReplacements": [{"replace": "apps/admin-frontend/src/environments/environment.ts", "with": "apps/admin-frontend/src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "admin-frontend:build", "hmr": true, "port": 3001}, "configurations": {"development": {"buildTarget": "admin-frontend:build:development"}, "production": {"buildTarget": "admin-frontend:build:production", "hmr": false}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/admin-frontend/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/admin-frontend/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}}, "tags": ["scope:admin", "type:app"]}