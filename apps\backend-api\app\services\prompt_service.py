"""
Prompt Service - Hanterar konfigurerbar prompts för fakturabehandling
"""

import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class PromptTemplate:
    """Template för en prompt med system och user meddelanden"""
    system_prompt: str
    user_prompt_template: str
    description: str


class PromptService:
    """Service för att hantera konfigurerbar prompts"""
    
    def __init__(self):
        self.prompts = self._load_default_prompts()
    
    def _load_default_prompts(self) -> Dict[str, PromptTemplate]:
        """Ladda standard prompts för fakturabehandling"""
        return {
            "extrahera": PromptTemplate(
                system_prompt="""Du är en expert på att extrahera strukturerad information från fakturor. 
Din uppgift är att analysera fakturainnehåll och extrahera viktig information i markdown format.

Svara alltid i följande JSON struktur:
{
    "data": "extraherad information i markdown format",
    "reasoning": "förklaring av hur du resonerat"
}""",
                user_prompt_template="""Analysera följande faktura och extrahera all viktig information:

{file_data}

Extrahera följande information och formatera som markdown:
- Leverantörsinformation (namn, adress, organisationsnummer)
- Fakturanummer och datum
- Förfallodatum
- Totalsumma och valuta
- Fakturaraderna med beskrivning, kvantitet, pris
- Eventuell moms/skatt information
- Betalningsvillkor
- Referensnummer

Var noggrann och extrahera all tillgänglig information.""",
                description="Extraherar strukturerad information från fakturainnehåll"
            ),
            
            "kontext": PromptTemplate(
                system_prompt="""Du är en expert på att berika fakturakontext med relevant affärsinformation.
Din uppgift är att analysera extraherad fakturainformation och skapa en rikare kontext.

Svara alltid i följande JSON struktur:
{
    "data": "berikad sammanfattning i markdown format",
    "reasoning": "förklaring av hur du resonerat"
}""",
                user_prompt_template="""Baserat på följande extraherade fakturainformation, skapa en berikad kontext:

{extracted_data}

Skapa en berikad sammanfattning som inkluderar:
- Analys av leverantören och deras bransch
- Kategorisering av vad som köpts (tjänster, varor, etc.)
- Affärskontext och syfte med köpet
- Eventuella säsongsmönster eller återkommande kostnader
- Riskbedömning och compliance-aspekter
- Förslag på kostnadskategorier

Formatera svaret som markdown för enkel läsning.""",
                description="Skapar berikad kontext kring fakturan och leverantören"
            ),
            
            "hitta_konto": PromptTemplate(
                system_prompt="""Du är en expert på redovisning och kontoplanering.
Din uppgift är att föreslå lämpliga redovisningskonton baserat på fakturakontext.

Svara alltid i följande JSON struktur:
{
    "data": [
        {
            "account_code": "kontonummer",
            "account_name": "kontonamn", 
            "amount": belopp,
            "description": "beskrivning",
            "confidence": 0.95
        }
    ],
    "reasoning": "förklaring av hur du resonerat"
}""",
                user_prompt_template="""Baserat på följande berikade fakturasammanfattning, föreslå lämpliga redovisningskonton:

{context_data}

Analysera informationen och föreslå:
- Lämpliga konton från svensk kontoplan (BAS-kontoplanen)
- Fördelning av belopp på olika konton om nödvändigt
- Moms/skattekonton om tillämpligt
- Konfidensgrad för varje förslag (0.0-1.0)

Tänk på:
- Svensk redovisningsstandard
- Vanliga affärstransaktioner
- Moms och skatteregler
- Kostnadskategorisering

Returnera en JSON array med kontoinformation.""",
                description="Identifierar lämpliga redovisningskonton med RAG-sökning"
            ),
            
            "bokfora": PromptTemplate(
                system_prompt="""Du är en expert på bokföring och ERP-system integration.
Din uppgift är att förbereda bokföringsdata för ERP-system.

Svara alltid i följande JSON struktur:
{
    "data": {
        "booking_entries": [...],
        "status": "prepared/error",
        "erp_format": "format_name"
    },
    "reasoning": "förklaring av hur du resonerat"
}""",
                user_prompt_template="""Förbered bokföringsdata för ERP-system baserat på:

Fakturakontext: {context_data}
Identifierade konton: {account_data}
ERP-system: {erp_system}

Skapa bokföringsdata som är redo för {erp_system} integration:
- Formatera enligt ERP-systemets krav
- Validera att debet = kredit
- Inkludera alla nödvändiga fält
- Hantera moms och skatter korrekt
- Sätt lämpliga bokföringsdatum

Returnera strukturerad data som kan skickas direkt till ERP-systemet.""",
                description="Förbereder och validerar bokföringsdata för ERP-integration"
            )
        }
    
    def get_prompt(self, step_name: str) -> Optional[PromptTemplate]:
        """Hämta prompt template för ett specifikt steg"""
        return self.prompts.get(step_name.lower())
    
    def format_prompt(self, step_name: str, **kwargs) -> tuple[str, str]:
        """Formatera prompt med givna parametrar"""
        template = self.get_prompt(step_name)
        if not template:
            raise ValueError(f"Unknown prompt step: {step_name}")
        
        try:
            user_prompt = template.user_prompt_template.format(**kwargs)
            return template.system_prompt, user_prompt
        except KeyError as e:
            raise ValueError(f"Missing required parameter for prompt {step_name}: {e}")
    
    def list_available_prompts(self) -> Dict[str, str]:
        """Lista alla tillgängliga prompts med beskrivningar"""
        return {name: template.description for name, template in self.prompts.items()}
    
    def update_prompt(self, step_name: str, system_prompt: str, user_prompt_template: str, description: str):
        """Uppdatera en prompt (för framtida admin-funktionalitet)"""
        self.prompts[step_name.lower()] = PromptTemplate(
            system_prompt=system_prompt,
            user_prompt_template=user_prompt_template,
            description=description
        )
        logger.info(f"Updated prompt for step: {step_name}")


# Global instance
_prompt_service: Optional[PromptService] = None


def get_prompt_service() -> PromptService:
    """Få global prompt service instance"""
    global _prompt_service
    if _prompt_service is None:
        _prompt_service = PromptService()
    return _prompt_service


def reset_prompt_service():
    """Reset global prompt service (för testing)"""
    global _prompt_service
    _prompt_service = None
