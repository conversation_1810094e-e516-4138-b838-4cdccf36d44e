{"name": "app-frontend", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/app-frontend/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/app-frontend", "index": "apps/app-frontend/public/index.html", "baseHref": "/", "main": "apps/app-frontend/src/index.tsx", "polyfills": "apps/app-frontend/src/polyfills.ts", "tsConfig": "apps/app-frontend/tsconfig.app.json", "assets": ["apps/app-frontend/public/favicon.ico", "apps/app-frontend/public"], "styles": ["apps/app-frontend/src/index.css"], "scripts": [], "webpackConfig": "@nx/react/plugins/webpack"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true}, "production": {"fileReplacements": [{"replace": "apps/app-frontend/src/environments/environment.ts", "with": "apps/app-frontend/src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "app-frontend:build", "hmr": true, "port": 3002}, "configurations": {"development": {"buildTarget": "app-frontend:build:development"}, "production": {"buildTarget": "app-frontend:build:production", "hmr": false}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/app-frontend/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/app-frontend/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}}, "tags": ["scope:app", "type:app"]}