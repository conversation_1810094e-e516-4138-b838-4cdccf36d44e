# Database Configuration
POSTGRES_DB=aggie_prod
POSTGRES_USER=aggie_user
POSTGRES_PASSWORD=secure_password_here

# Redis Configuration
REDIS_PASSWORD=secure_redis_password_here

# JWT & Encryption
JWT_SECRET_KEY=your-super-secure-jwt-secret-key-here-at-least-32-chars
ENCRYPTION_KEY=your-32-character-encryption-key-here

# LLM Configuration
LLM_PROVIDER=openai  # or azure
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=o1-mini  # or gpt-4o-mini, gpt-4o, o1-preview

# Azure OpenAI Configuration (only needed if LLM_PROVIDER=azure)
AZURE_OPENAI_API_KEY=your-azure-openai-api-key-here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT_NAME=your-deployment-name
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>

# Frontend Configuration
FRONTEND_API_URL=https://api.yourcompany.com

# Invoice Integration OAuth2 Settings
FORTNOX_CLIENT_ID=your-fortnox-client-id
FORTNOX_CLIENT_SECRET=your-fortnox-client-secret
FORTNOX_API_URL=https://api.fortnox.se/3/

VISMA_CLIENT_ID=your-visma-client-id
VISMA_CLIENT_SECRET=your-visma-client-secret
VISMA_API_URL=https://eaccountingapi.vismaonline.com/v2/
