import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { actionItemsApi } from '../services/api';
import { useTenant } from '../contexts/TenantContext';
import { ActionItem } from '../types';
import toast from 'react-hot-toast';
import '../appCustomStyles.css';

export default function ActionItemsPage() {
  const [selectedPriority, setSelectedPriority] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showCompleted, setShowCompleted] = useState(false);
  const { currentTenant, hasPermission } = useTenant();
  const queryClient = useQueryClient();

  const { data: actionItems = [], isLoading, error } = useQuery(
    ['actionItems', currentTenant?.id, selectedPriority, selectedCategory, showCompleted],
    () => actionItemsApi.getActionItems({
      priority: selectedPriority || undefined,
      category: selectedCategory || undefined,
      completed: showCompleted ? undefined : false,
    }),
    {
      enabled: !!currentTenant,
      onError: (error: any) => {
        console.error('Action Items API Error:', error);
        console.log('Current tenant:', currentTenant);
        console.log('Tenant permissions:', currentTenant?.permissions);
        const message = error.response?.data?.detail || 'Failed to load action items';
        toast.error(message);
      }
    }
  );

  const completeMutation = useMutation(
    ({ id, notes }: { id: string; notes?: string }) =>
      actionItemsApi.completeActionItem(id, notes),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['actionItems']);
        toast.success('Action item completed!');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to complete action item');
      },
    }
  );

  const reopenMutation = useMutation(
    (id: string) => actionItemsApi.reopenActionItem(id),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['actionItems']);
        toast.success('Action item reopened!');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to reopen action item');
      },
    }
  );



  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'review':
        return <CheckCircleIcon className="h-5 w-5 text-orange-500" />;
      default:
        return <CheckCircleIcon className="h-5 w-5 text-blue-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (!currentTenant) {
    return (
      <div className="page-background">
        <div className="content-container">
          <div className="card-container text-center">
            <p className="text-gray-500">Please select a tenant to view action items.</p>
          </div>
        </div>
      </div>
    );
  }

  // Check if user has permission to read action items
  if (!hasPermission('action_items:read')) {
    return (
      <div className="page-background">
        <div className="content-container">
          <div className="card-container empty-state">
            <ExclamationTriangleIcon className="empty-state-icon" />
            <h3 className="empty-state-title">Access Denied</h3>
            <p className="empty-state-description">
              You don't have permission to view action items.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="page-background">
        <div className="content-container">
          <div className="card-container empty-state">
            <ExclamationTriangleIcon className="empty-state-icon text-red-400" />
            <h3 className="empty-state-title">Error Loading Action Items</h3>
            <p className="empty-state-description">
              {(error as any)?.response?.data?.detail || 'Failed to load action items'}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="btn-small mt-4"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-background">
      <div className="content-container">
        <div className="card-container-large mb-8">
          <h1 className="page-title custom-gradient-text">Action Items</h1>
          <p className="text-gray-700">
            Tasks and items requiring attention for {currentTenant.name}
          </p>
        </div>

        {/* Filters */}
        <div className="filter-container">
          <h3 className="section-title">Filters</h3>
          <div className="filter-grid">
            <select
              value={selectedPriority}
              onChange={(e) => setSelectedPriority(e.target.value)}
              className="select-custom"
            >
              <option value="">All Priorities</option>
              <option value="urgent">Urgent</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>

            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="select-custom"
            >
              <option value="">All Categories</option>
              <option value="review">Review</option>
              <option value="validation">Validation</option>
              <option value="error">Error</option>
              <option value="manual_entry">Manual Entry</option>
            </select>

            <label className="flex items-center bg-white rounded-2xl px-4 py-3 shadow-sm">
              <input
                type="checkbox"
                checked={showCompleted}
                onChange={(e) => setShowCompleted(e.target.checked)}
                className="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              />
              <span className="ml-2 text-sm text-gray-700 font-medium">Show completed</span>
            </label>
          </div>
        </div>

        {/* Action Items List */}
        <div className="card-container">
          <h3 className="section-title">Action Items</h3>
          {isLoading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
            </div>
          ) : actionItems.length === 0 ? (
            <div className="empty-state">
              <p className="text-gray-500">No action items found</p>
            </div>
          ) : (
            <div className="space-y-6">
              {actionItems.map((item: ActionItem) => (
                <div
                  key={item.id}
                  className={`bg-gradient-to-r from-purple-50 via-indigo-50 to-pink-50 rounded-2xl p-6 shadow-sm border border-purple-100 ${
                    item.is_completed ? 'opacity-75' : ''
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0 mt-1">
                        {getCategoryIcon(item.category)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-wrap items-center gap-2 mb-2">
                          <h3 className={`text-lg font-semibold ${
                            item.is_completed ? 'line-through text-gray-500' : 'text-gray-900'
                          }`}>
                            {item.title}
                          </h3>
                          <span className={`badge-${item.priority === 'urgent' ? 'error' : item.priority === 'high' ? 'warning' : item.priority === 'medium' ? 'info' : 'success'}`}>
                            {item.priority}
                          </span>
                          <span className="badge-info">
                            {item.category}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">
                          {item.description}
                        </p>
                        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                          <span>Created: {formatDate(item.created_at)}</span>
                          {item.assigned_user && (
                            <span>Assigned to: {item.assigned_user.email}</span>
                          )}
                          {item.invoice && (
                            <span>Invoice: {item.invoice.supplier_name}</span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex-shrink-0 flex space-x-2">
                      {!item.is_completed && hasPermission('action_items:write') && (
                        <button
                          onClick={() => completeMutation.mutate({ id: item.id })}
                          disabled={completeMutation.isLoading}
                          className="btn-small bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircleIcon className="h-4 w-4 mr-1" />
                          Complete
                        </button>
                      )}
                      {item.is_completed && hasPermission('action_items:write') && (
                        <button
                          onClick={() => reopenMutation.mutate(item.id)}
                          disabled={reopenMutation.isLoading}
                          className="btn-small bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
                        >
                          Reopen
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
