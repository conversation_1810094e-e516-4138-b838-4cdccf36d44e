/* Custom styles for modern Aggie application design */

/* === LAYOUT & BACKGROUNDS === */
.page-background {
  @apply min-h-screen bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50;
}

.content-container {
  @apply max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8;
}

.card-container {
  @apply bg-white bg-opacity-90 rounded-3xl shadow-2xl border border-purple-200 p-6;
}

.card-container-large {
  @apply bg-white bg-opacity-90 rounded-3xl shadow-2xl border border-purple-200 p-8;
}

/* === TYPOGRAPHY === */
.custom-gradient-text {
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.page-title {
  @apply text-3xl font-bold text-gray-900 mb-6;
}

.section-title {
  @apply text-xl font-semibold text-gray-800 mb-4;
}

/* === FORM ELEMENTS === */
.input-custom {
  @apply appearance-none relative block w-full px-4 py-3 border border-gray-300 placeholder-gray-400 text-gray-900 rounded-2xl focus:outline-none focus:ring-4 focus:ring-indigo-300 focus:border-indigo-500 sm:text-base shadow-sm transition-shadow;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
  font-weight: 500;
}

.input-custom::placeholder {
  color: #a5b4fc;
  opacity: 1;
}

.input-custom:focus {
  box-shadow: 0 4px 14px rgba(99, 102, 241, 0.4);
}

.select-custom {
  @apply appearance-none relative block w-full px-4 py-3 border border-gray-300 text-gray-900 rounded-2xl focus:outline-none focus:ring-4 focus:ring-indigo-300 focus:border-indigo-500 sm:text-base shadow-sm transition-shadow bg-white;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15);
  font-weight: 500;
}

.select-custom:focus {
  box-shadow: 0 4px 14px rgba(99, 102, 241, 0.4);
}

/* === BUTTONS === */
.btn-primary {
  @apply w-full flex justify-center py-3 px-5 border border-transparent text-lg font-semibold rounded-3xl text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-4 focus:ring-indigo-300 disabled:opacity-60 disabled:cursor-not-allowed transition-all;
}

.btn-secondary {
  @apply w-full flex justify-center py-3 px-5 border border-indigo-300 text-lg font-semibold rounded-3xl text-indigo-600 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-4 focus:ring-indigo-300 disabled:opacity-60 disabled:cursor-not-allowed transition-all;
}

.btn-danger {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-60 disabled:cursor-not-allowed transition-all;
}

.btn-small {
  @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-2xl text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all;
}

/* === BUTTON EFFECTS === */
button:focus {
  outline-offset: 2px;
}

button {
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

button:hover:not(:disabled) {
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.4);
}

button:disabled {
  transition: opacity 0.3s ease;
}

/* === STATS & METRICS === */
.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8;
}

.stat-card {
  @apply bg-gradient-to-r from-purple-100 via-indigo-100 to-pink-100 rounded-3xl p-6 shadow-lg border border-purple-200;
}

.stat-value {
  @apply text-3xl font-bold text-indigo-700 mb-2;
}

.stat-label {
  @apply text-sm font-medium text-gray-600;
}

/* === TABLES === */
.table-container {
  @apply bg-gradient-to-r from-purple-100 via-indigo-100 to-pink-100 rounded-3xl p-6 shadow-lg;
}

.table-custom {
  @apply min-w-full divide-y divide-gray-200 bg-white rounded-2xl overflow-hidden shadow-sm;
}

.table-header {
  @apply bg-gradient-to-r from-indigo-50 to-purple-50;
}

.table-header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-row {
  @apply bg-white hover:bg-gray-50 transition-colors;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* === BADGES & STATUS === */
.badge-success {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800;
}

.badge-warning {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800;
}

.badge-error {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800;
}

.badge-info {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800;
}

/* === FILTERS & CONTROLS === */
.filter-container {
  @apply bg-gradient-to-r from-purple-100 via-indigo-100 to-pink-100 rounded-3xl p-6 shadow-lg mb-6;
}

.filter-grid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-4;
}

/* === LOADING STATES === */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600;
}

.loading-container {
  @apply flex items-center justify-center py-12;
}

/* === EMPTY STATES === */
.empty-state {
  @apply text-center py-12;
}

.empty-state-icon {
  @apply mx-auto h-12 w-12 text-gray-400 mb-4;
}

.empty-state-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.empty-state-description {
  @apply text-sm text-gray-500;
}

/* === LOGOTYPES === */
.logo-and-text {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
}

.small-logo-50 {
  height: 50px;
  width: 50px;
}