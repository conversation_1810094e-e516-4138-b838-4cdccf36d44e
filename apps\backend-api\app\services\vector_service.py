from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Tuple
import hashlib
import logging

from app.models.invoice import InvoiceVector, Invoice
from app.services.llm_provider import get_provider_instance

logger = logging.getLogger(__name__)


class VectorService:
    """Service for managing vector embeddings and similarity search"""
    
    def __init__(self):
        self.llm_provider = get_provider_instance()
    
    async def store_invoice_embedding(
        self, 
        db: Session, 
        invoice_id: str, 
        tenant_id: str, 
        content: str
    ) -> InvoiceVector:
        """Generate and store embedding for invoice content"""
        try:
            # Generate content hash
            content_hash = hashlib.sha256(content.encode()).hexdigest()
            
            # Check if embedding already exists for this content
            existing = db.query(InvoiceVector).filter(
                InvoiceVector.invoice_id == invoice_id,
                InvoiceVector.content_hash == content_hash
            ).first()
            
            if existing:
                logger.info(f"Embedding already exists for invoice {invoice_id}")
                return existing
            
            # Generate embedding
            embedding = await self.llm_provider.generate_embedding(content)
            
            # Store in database
            invoice_vector = InvoiceVector(
                invoice_id=invoice_id,
                tenant_id=tenant_id,
                embedding=embedding,
                content_hash=content_hash
            )
            
            db.add(invoice_vector)
            db.commit()
            db.refresh(invoice_vector)
            
            logger.info(f"Stored embedding for invoice {invoice_id}")
            return invoice_vector
            
        except Exception as e:
            logger.error(f"Error storing embedding for invoice {invoice_id}: {e}")
            db.rollback()
            raise
    
    async def find_similar_invoices(
        self, 
        db: Session, 
        tenant_id: str, 
        query_content: str, 
        limit: int = 5,
        similarity_threshold: float = 0.7
    ) -> List[Tuple[Invoice, float]]:
        """Find similar invoices using vector similarity search"""
        try:
            # Generate embedding for query content
            query_embedding = await self.llm_provider.generate_embedding(query_content)
            
            # Perform similarity search using pgvector
            # Using cosine similarity (1 - cosine distance)
            similarity_query = text("""
                SELECT 
                    i.id,
                    i.supplier_name,
                    i.extracted_context,
                    i.status,
                    i.created_at,
                    1 - (iv.embedding <=> :query_embedding) as similarity
                FROM invoices i
                JOIN invoice_vectors iv ON i.id = iv.invoice_id
                WHERE i.tenant_id = :tenant_id
                    AND i.status = 'completed'
                    AND 1 - (iv.embedding <=> :query_embedding) > :threshold
                ORDER BY similarity DESC
                LIMIT :limit
            """)
            
            result = db.execute(similarity_query, {
                'query_embedding': query_embedding,
                'tenant_id': tenant_id,
                'threshold': similarity_threshold,
                'limit': limit
            })
            
            similar_invoices = []
            for row in result:
                # Fetch full invoice object
                invoice = db.query(Invoice).filter(Invoice.id == row.id).first()
                if invoice:
                    similar_invoices.append((invoice, row.similarity))
            
            logger.info(f"Found {len(similar_invoices)} similar invoices for tenant {tenant_id}")
            return similar_invoices
            
        except Exception as e:
            logger.error(f"Error finding similar invoices: {e}")
            raise
    
    async def get_similar_contexts(
        self, 
        db: Session, 
        tenant_id: str, 
        query_content: str, 
        limit: int = 3
    ) -> List[str]:
        """Get contexts from similar invoices for RAG"""
        try:
            similar_invoices = await self.find_similar_invoices(
                db, tenant_id, query_content, limit
            )
            
            contexts = []
            for invoice, similarity in similar_invoices:
                if invoice.extracted_context:
                    contexts.append(invoice.extracted_context)
            
            return contexts
            
        except Exception as e:
            logger.error(f"Error getting similar contexts: {e}")
            return []  # Return empty list on error to allow processing to continue
    
    def update_embedding(
        self, 
        db: Session, 
        invoice_vector: InvoiceVector, 
        new_content: str
    ) -> InvoiceVector:
        """Update existing embedding with new content"""
        try:
            # Generate new content hash
            new_hash = hashlib.sha256(new_content.encode()).hexdigest()
            
            # If content hasn't changed, no need to update
            if invoice_vector.content_hash == new_hash:
                return invoice_vector
            
            # Generate new embedding (this should be async but keeping sync for simplicity)
            # In production, you might want to queue this as a separate task
            import asyncio
            new_embedding = asyncio.run(self.llm_provider.generate_embedding(new_content))
            
            # Update the record
            invoice_vector.embedding = new_embedding
            invoice_vector.content_hash = new_hash
            
            db.commit()
            db.refresh(invoice_vector)
            
            logger.info(f"Updated embedding for invoice {invoice_vector.invoice_id}")
            return invoice_vector
            
        except Exception as e:
            logger.error(f"Error updating embedding: {e}")
            db.rollback()
            raise
    
    def delete_embedding(self, db: Session, invoice_id: str):
        """Delete embedding for an invoice"""
        try:
            db.query(InvoiceVector).filter(
                InvoiceVector.invoice_id == invoice_id
            ).delete()
            db.commit()
            
            logger.info(f"Deleted embedding for invoice {invoice_id}")
            
        except Exception as e:
            logger.error(f"Error deleting embedding for invoice {invoice_id}: {e}")
            db.rollback()
            raise


# Global service instance
_vector_service = None


def get_vector_service() -> VectorService:
    """Get singleton vector service instance"""
    global _vector_service
    if _vector_service is None:
        _vector_service = VectorService()
    return _vector_service
