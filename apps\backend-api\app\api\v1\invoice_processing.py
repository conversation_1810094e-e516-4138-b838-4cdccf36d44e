"""
API endpoints för det nya fakturabehandlingssystemet
"""

import logging
from typing import Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks

from app.dependencies.auth import CurrentTenantUser, TenantScopedDB
from app.models import Invoice, Session as ProcessingSession
from app.schemas.invoice_processing import (
    InvoiceCreateRequest, InvoiceResponse, SessionResponse, SessionDetailResponse,
    SessionsSummaryResponse, SessionRetryRequest, StepExecuteRequest,
    ProcessingTaskResponse, LLMProviderInfoResponse, PromptListResponse,
    StepExecutionResponse, SessionStatus
)
from app.services.session_log_service import get_session_log_service
from app.services.llm_provider import get_llm_service
from app.services.prompt_service import get_prompt_service
from app.tasks.invoice_processing_tasks import process_invoice_task, retry_invoice_processing_task, process_step_only_task
from app.utils.permissions import Permission, check_permission
from app.routers.base import handle_database_error

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/invoices", response_model=ProcessingTaskResponse)
async def create_invoice(
    request: InvoiceCreateRequest,
    background_tasks: BackgroundTasks,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Skapa en ny faktura och starta bearbetning"""
    # Kontrollera behörigheter
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    try:
        # Skapa faktura
        invoice = Invoice(
            tenant_id=tenant_user.tenant_id,
            import_typ=request.import_typ.value,
            file_data=request.file_data,
            metadata_ERP=request.metadata_ERP,
            original_filename=request.original_filename,
            file_type=request.file_type,
            status="pending"
        )
        
        db.add(invoice)
        db.commit()
        db.refresh(invoice)
        
        # Skapa session
        session = ProcessingSession(
            tenant_id=tenant_user.tenant_id,
            invoice_id=invoice.id,
            status="pending"
        )
        
        db.add(session)
        db.commit()
        db.refresh(session)
        
        # Starta bearbetning i bakgrunden
        task = process_invoice_task.delay(
            invoice_id=str(invoice.id),
            tenant_id=str(tenant_user.tenant_id)
        )
        
        logger.info(f"Created invoice {invoice.id} and started processing task {task.id}")
        
        return ProcessingTaskResponse(
            task_id=task.id,
            session_id=session.id,
            status="started",
            message="Invoice created and processing started"
        )
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating invoice: {e}")
        raise handle_database_error(e)


@router.get("/sessions", response_model=SessionsSummaryResponse)
async def get_sessions(
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB,
    limit: int = 50,
    offset: int = 0,
    status_filter: Optional[SessionStatus] = None
):
    """Hämta sessions sammanfattning"""
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    try:
        log_service = get_session_log_service(db)
        result = log_service.get_sessions_summary(
            tenant_id=tenant_user.tenant_id,
            limit=limit,
            offset=offset,
            status_filter=status_filter.value if status_filter else None
        )
        
        return SessionsSummaryResponse(**result)
        
    except Exception as e:
        logger.error(f"Error getting sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/sessions/{session_id}", response_model=SessionDetailResponse)
async def get_session_detail(
    session_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Hämta detaljerad session information"""
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    try:
        log_service = get_session_log_service(db)
        result = log_service.get_session_detail(session_id, tenant_user.tenant_id)
        
        return SessionDetailResponse(**result)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting session detail: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/sessions/{session_id}/retry", response_model=ProcessingTaskResponse)
async def retry_session(
    session_id: UUID,
    request: SessionRetryRequest,
    background_tasks: BackgroundTasks,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Återförsök session bearbetning"""
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    try:
        # Kontrollera att session finns
        session = db.query(ProcessingSession).filter(
            ProcessingSession.id == session_id,
            ProcessingSession.tenant_id == tenant_user.tenant_id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        # Starta retry task
        task = retry_invoice_processing_task.delay(
            invoice_id=str(session.invoice_id),
            tenant_id=str(tenant_user.tenant_id),
            from_step=request.from_step.value if request.from_step else None
        )
        
        logger.info(f"Started retry task {task.id} for session {session_id}")
        
        return ProcessingTaskResponse(
            task_id=task.id,
            session_id=session_id,
            status="retry_started",
            message=f"Retry started from step: {request.from_step.value if request.from_step else 'beginning'}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrying session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/sessions/{session_id}/execute-step", response_model=ProcessingTaskResponse)
async def execute_step(
    session_id: UUID,
    request: StepExecuteRequest,
    background_tasks: BackgroundTasks,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Kör ett specifikt bearbetningssteg"""
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    try:
        # Kontrollera att session finns
        session = db.query(ProcessingSession).filter(
            ProcessingSession.id == session_id,
            ProcessingSession.tenant_id == tenant_user.tenant_id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        # Starta step task
        task = process_step_only_task.delay(
            invoice_id=str(session.invoice_id),
            tenant_id=str(tenant_user.tenant_id),
            step_name=request.step_name.value
        )
        
        logger.info(f"Started step execution task {task.id} for session {session_id}, step {request.step_name.value}")
        
        return ProcessingTaskResponse(
            task_id=task.id,
            session_id=session_id,
            status="step_started",
            message=f"Step '{request.step_name.value}' execution started"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing step: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.delete("/sessions/{session_id}")
async def delete_session(
    session_id: UUID,
    tenant_user: CurrentTenantUser,
    db: TenantScopedDB
):
    """Radera en session och relaterad faktura"""
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    try:
        # Hämta session
        session = db.query(ProcessingSession).filter(
            ProcessingSession.id == session_id,
            ProcessingSession.tenant_id == tenant_user.tenant_id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        # Hämta relaterad faktura
        invoice = db.query(Invoice).filter(Invoice.id == session.invoice_id).first()
        
        # Radera session (cascade raderar loggar)
        db.delete(session)
        
        # Radera faktura om den finns
        if invoice:
            db.delete(invoice)
        
        db.commit()
        
        logger.info(f"Deleted session {session_id} and related invoice")
        
        return {"message": "Session and related invoice deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting session: {e}")
        raise handle_database_error(e)


@router.get("/llm-provider", response_model=LLMProviderInfoResponse)
async def get_llm_provider_info(tenant_user: CurrentTenantUser):
    """Hämta information om aktiv LLM provider"""
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    try:
        llm_service = get_llm_service()
        info = llm_service.get_provider_info()
        
        return LLMProviderInfoResponse(**info)
        
    except Exception as e:
        logger.error(f"Error getting LLM provider info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/prompts", response_model=PromptListResponse)
async def get_available_prompts(tenant_user: CurrentTenantUser):
    """Hämta tillgängliga prompts"""
    if not check_permission(tenant_user.role.permissions, Permission.INVOICES_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    try:
        prompt_service = get_prompt_service()
        prompts = prompt_service.list_available_prompts()
        
        return PromptListResponse(prompts=prompts)
        
    except Exception as e:
        logger.error(f"Error getting prompts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
