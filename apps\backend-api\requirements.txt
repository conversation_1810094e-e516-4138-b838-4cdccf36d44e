# Core FastAPI and web server
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0
watchdog==3.0.0  # Memory-efficient file watcher for development

# Database
sqlalchemy==2.0.23
sqlalchemy-utils==0.41.1
alembic==1.12.1
psycopg2-binary==2.9.9
pgvector==0.2.4

# Background tasks
redis==5.0.1
celery==5.3.4
nest-asyncio==1.5.8

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
cryptography==42.0.8
pyotp==2.9.0

# Data validation and settings
pydantic==2.5.0
pydantic-settings==2.1.0
# email-validator==2.1.0  # Replaced with custom validator (app/utils/email_validator.py)
python-dotenv==1.0.0

# File handling
python-multipart==0.0.6
aiofiles==23.2.1

# AI and ML
openai>=1.57.0

# Image and PDF processing
# pytesseract==0.3.10  # Removed - using multimodal LLM/external API instead
pdf2image>=1.17.0
pillow>=11.0.0
pypdf>=5.9.0  # Replaced PyPDF2 with pypdf (actively maintained successor)

# QR codes for 2FA
qrcode[pil]==7.4.2

# HTTP client and OAuth
httpx>=0.25.2  # Primary async HTTP client
requests>=2.31.0  # For sync HTTP requests when needed
# aiohttp==3.9.1  # Removed - using httpx instead (no need for two async HTTP clients)
authlib>=1.2.1

# Encryption (version already specified above with python-jose)

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
pytest-cov==4.1.0
factory-boy==3.3.0
